import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

void main() {
  group('Multi-Select Data Flow Logic Tests', () {
    late Question testQuestion;
    late QuestionPart testQuestionPart;

    setUp(() {
      // Create test question with multi-select measurement
      testQuestion = Question(
        questionId: 1,
        questionDescription: 'Test Question',
        isMll: false,
        measurements: [
          Measurement(
            measurementId: 1,
            measurementTypeId: 6, // Multi-select type
            measurementDescription: 'Test Multi-Select',
            measurementOptions: [
              MeasurementOption(
                measurementOptionId: 1,
                measurementOptionDescription: 'Option 1',
              ),
              MeasurementOption(
                measurementOptionId: 2,
                measurementOptionDescription: 'Option 2',
              ),
              MeasurementOption(
                measurementOptionId: 3,
                measurementOptionDescription: 'Option 3',
              ),
            ],
          ),
        ],
      );

      testQuestionPart = QuestionPart(
        questionpartId: 1,
        questionpartDescription: 'Test Question Part',
      );
    });

    test('should restore multi-select option IDs from saved data correctly', () {
      // Create a QuestionAnswerModel with saved multi-select data
      final questionAnswer = QuestionAnswerModel(
        taskId: 1,
        formId: 1,
        questionId: 1,
        questionpartId: 1,
        measurementId: 1,
        measurementTypeId: 6,
        measurementOptionIds: '1,3', // Saved as comma-separated IDs
        measurementTextResult: 'Option 1|Option 3', // Saved as pipe-separated descriptions
      );

      // Test the restoration logic (simulating the _restoreValueFromQuestionAnswer method)
      List<String> restoredValue = [];
      if (questionAnswer.measurementOptionIds != null &&
          questionAnswer.measurementOptionIds!.isNotEmpty) {
        restoredValue = questionAnswer.measurementOptionIds!
            .split(',')
            .map((id) => id.trim())
            .where((id) => id.isNotEmpty)
            .toList();
      }

      // Verify that the restored value contains option IDs (not descriptions)
      expect(restoredValue, isA<List<String>>());
      expect(restoredValue, contains('1'));
      expect(restoredValue, contains('3'));
      expect(restoredValue.length, equals(2));
    });

    test('should generate correct QuestionAnswer from multi-select option IDs', () {
      // Simulate user selecting options (IDs as strings)
      final selectedIds = ['1', '3'];
      final measurement = testQuestion.measurements!.first;

      // Test the generation logic (simulating the _generateQuestionAnswers method)
      final questionAnswer = QuestionAnswer(
        taskId: 1,
        flip: testQuestion.flip,
        formId: 1,
        questionId: testQuestion.questionId,
        isComment: false,
        commentTypeId: null,
        questionpartId: testQuestionPart.questionpartId,
        questionPartMultiId: testQuestionPart.questionpartId?.toString(),
        measurementId: measurement.measurementId,
        measurementTypeId: measurement.measurementTypeId,
      );

      // Multi-select logic
      final optionIds = selectedIds
          .map((id) => int.tryParse(id))
          .where((id) => id != null)
          .cast<int>()
          .toList();

      final selectedDescriptions = <String>[];
      for (final optionId in optionIds) {
        final option = measurement.measurementOptions!.firstWhere(
            (opt) => opt.measurementOptionId == optionId,
            orElse: () => MeasurementOption());
        if (option.measurementOptionDescription != null) {
          selectedDescriptions.add(option.measurementOptionDescription!);
        }
      }

      questionAnswer.measurementOptionId = null;
      questionAnswer.measurementOptionIds = optionIds.join(',');
      questionAnswer.measurementTextResult = selectedDescriptions.join('|');

      // Verify that option IDs are saved correctly
      expect(questionAnswer.measurementOptionIds, equals('1,3'));
      
      // Verify that descriptions are saved in text result
      expect(questionAnswer.measurementTextResult, equals('Option 1|Option 3'));
      
      // Verify other fields
      expect(questionAnswer.measurementId, equals(1));
      expect(questionAnswer.measurementTypeId, equals(6));
      expect(questionAnswer.measurementOptionId, isNull); // Should be null for multi-select
    });

    test('should handle empty multi-select values correctly', () {
      // Test with empty selection
      final selectedIds = <String>[];
      final measurement = testQuestion.measurements!.first;

      final questionAnswer = QuestionAnswer(
        taskId: 1,
        flip: testQuestion.flip,
        formId: 1,
        questionId: testQuestion.questionId,
        isComment: false,
        commentTypeId: null,
        questionpartId: testQuestionPart.questionpartId,
        questionPartMultiId: testQuestionPart.questionpartId?.toString(),
        measurementId: measurement.measurementId,
        measurementTypeId: measurement.measurementTypeId,
      );

      // Handle empty values
      if (selectedIds.isEmpty) {
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = null;
      }

      // Verify that empty values are handled correctly
      expect(questionAnswer.measurementOptionIds, isNull);
      expect(questionAnswer.measurementTextResult, isNull);
      expect(questionAnswer.measurementOptionId, isNull);
    });

    test('should handle invalid option IDs gracefully', () {
      // Test with invalid option IDs
      final selectedIds = ['999', '1000']; // Non-existent option IDs
      final measurement = testQuestion.measurements!.first;

      final questionAnswer = QuestionAnswer(
        taskId: 1,
        flip: testQuestion.flip,
        formId: 1,
        questionId: testQuestion.questionId,
        isComment: false,
        commentTypeId: null,
        questionpartId: testQuestionPart.questionpartId,
        questionPartMultiId: testQuestionPart.questionpartId?.toString(),
        measurementId: measurement.measurementId,
        measurementTypeId: measurement.measurementTypeId,
      );

      // Multi-select logic with invalid IDs
      final optionIds = selectedIds
          .map((id) => int.tryParse(id))
          .where((id) => id != null)
          .cast<int>()
          .toList();

      final selectedDescriptions = <String>[];
      for (final optionId in optionIds) {
        final option = measurement.measurementOptions!.firstWhere(
            (opt) => opt.measurementOptionId == optionId,
            orElse: () => MeasurementOption());
        if (option.measurementOptionDescription != null) {
          selectedDescriptions.add(option.measurementOptionDescription!);
        }
      }

      questionAnswer.measurementOptionId = null;
      questionAnswer.measurementOptionIds = optionIds.join(',');
      questionAnswer.measurementTextResult = selectedDescriptions.join('|');

      // Should save the IDs but have empty text result since descriptions don't exist
      expect(questionAnswer.measurementOptionIds, equals('999,1000'));
      expect(questionAnswer.measurementTextResult, equals('')); // Empty because no valid descriptions found
    });

    test('should restore empty multi-select values correctly', () {
      // Create a QuestionAnswerModel with empty multi-select data
      final questionAnswer = QuestionAnswerModel(
        taskId: 1,
        formId: 1,
        questionId: 1,
        questionpartId: 1,
        measurementId: 1,
        measurementTypeId: 6,
        measurementOptionIds: null,
        measurementTextResult: null,
      );

      // Test restoration of empty values
      List<String> restoredValue = [];
      if (questionAnswer.measurementOptionIds != null &&
          questionAnswer.measurementOptionIds!.isNotEmpty) {
        restoredValue = questionAnswer.measurementOptionIds!
            .split(',')
            .map((id) => id.trim())
            .where((id) => id.isNotEmpty)
            .toList();
      }

      // Verify that empty values are restored correctly
      expect(restoredValue, isA<List<String>>());
      expect(restoredValue.isEmpty, isTrue);
    });

    test('should convert option IDs to descriptions for display', () {
      // Test the display conversion logic (used in MultiSelectWidget)
      final selectedIds = ['1', '3'];
      final measurement = testQuestion.measurements!.first;
      final options = measurement.measurementOptions ?? [];

      // Convert option IDs to descriptions for display
      final selectedDescriptions = selectedIds
          .map((id) {
            final option = options.firstWhere(
              (opt) => opt.measurementOptionId?.toString() == id,
              orElse: () => MeasurementOption(),
            );
            return option.measurementOptionDescription ?? 'Unknown';
          })
          .where((desc) => desc != 'Unknown')
          .toList();

      // Verify the conversion
      expect(selectedDescriptions, contains('Option 1'));
      expect(selectedDescriptions, contains('Option 3'));
      expect(selectedDescriptions.length, equals(2));
    });
  });
}
